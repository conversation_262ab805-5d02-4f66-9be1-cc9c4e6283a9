import {
    AuditorFrameworkOverhaulStatus as AuditOverhaulStatus,
    AuditorFrameworkType as AuditTypeEnum,
    ErrorCode,
    FrameworkTag,
    RequirementIndexCategory,
    RequirementIndexCategoryLabel,
    RequirementIndexCategoryName,
    SortDir,
    SortType,
} from '@drata/enums';
import { HttpService } from '@nestjs/axios';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AUDITOR_ENABLED_USER_FEATURE_TYPES } from 'app/audit-hub/constants/auditor.constants';
import { AuditAvailableRequestOwnersRequestDto } from 'app/audit-hub/dtos/audit-available-request-owners-request.dto';
import { AuditCustomerRequestControlEvidencePackageRequestDto } from 'app/audit-hub/dtos/audit-customer-request-control-evidence-package-request.dto';
import { AuditHubControlsPaginatedRequestDto } from 'app/audit-hub/dtos/audit-hub-controls-paginated-request.dto';
import { AuditHubEvidencesDownloadRequestDto } from 'app/audit-hub/dtos/audit-hub-evidences-download-request.dto';
import { AuditHubEvidencesPaginatedRequestDto } from 'app/audit-hub/dtos/audit-hub-evidences-paginated-request.dto';
import { AuditPaginatedRequestDto } from 'app/audit-hub/dtos/audit-paginated-request.dto';
import { AuditPersonnelRequestDto } from 'app/audit-hub/dtos/audit-personnel-request.dto';
import { AuditUpdateRequestDto } from 'app/audit-hub/dtos/audit-update-request.dto';
import { AuditorRequestDto } from 'app/audit-hub/dtos/auditor-request.dto';
import { AuditHubEvidenceType } from 'app/audit-hub/enums/audit-hub-evidence-type.enum';
import { ControlEvidenceWaasService } from 'app/audit-hub/evidence-waas-service/audithub-control-evidence-waas.service';
import { ControlEvidencePackageParams } from 'app/audit-hub/evidence-waas-service/types/audithub-control-evidence-params.types';
import {
    getAuditPeriodByFramework,
    isAuditorInAuditorMappingList,
    isAuditorMappingInAuditorList,
    isRegularEvidence,
    transformAuditorToAuditorFrameworkAuditor as transformAuditorToAuditAuditor,
} from 'app/audit-hub/helpers/audit.helper';
import { GenerateAuditCustomerRequestControlEvidencePackageEvent } from 'app/audit-hub/observables/events/generate-audit-customer-request-control-evidence-package.event';
import { AuditHubValidationOrchestrationService } from 'app/audit-hub/orchestration/audit-hub-validation-orchestration.service';
import { AuditHubValidationService } from 'app/audit-hub/services/audit-hub-validation.service';
import { AuditHubAuditPackagesOrchestrationService } from 'app/audit-hub/services/audithub-audit-packages/audit-hub-audit-packages-orchestration.service';
import { CollectAuditHubControlEvidenceService } from 'app/audit-hub/services/audithub-audit-packages/collect-audit-hub-control-evidence.service';
import {
    AuditHubControlEvidenceType,
    AuditHubEvidencePaginationType,
} from 'app/audit-hub/types/audit-hub-control-evidence.type';
import { AuditToAuditorsMappingsDifferenceSubsets } from 'app/audit-hub/types/audit-to-auditors-mappings-difference-subsets.type';
import { AuditWithAuditors } from 'app/audit-hub/types/audit-with-auditors.type';
import { ControlEvidenceCounter } from 'app/audit-hub/types/control-evidence-counter.type';
import { CustomerRequestMessageFileData } from 'app/audit-hub/types/customer-request-message-file-data.type';
import {
    FedRamp20xKSIValidationData,
    FedRamp20xKSIValidationStatus,
    RequirementCategory,
} from 'app/audit-hub/types/fedramp-20x-ksi-validation-data.type';
import { PaginatedCustomerRequestMessageFilesParams } from 'app/audit-hub/types/paginated-customer-request-message-files-params.type';
import { PublishAuditorRemovedFromAuditEventParams } from 'app/audit-hub/types/publish-auditor-mappings-changed-event-params.type';
import { PublishAuditorMappingsChangedEventParams } from 'app/audit-hub/types/publish-auditor-mappings-changed-events-params.type';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { CompaniesOrchestrationService } from 'app/companies/services/companies-orchestration.service';
import { ControlService } from 'app/control/control.service';
import { AuditorFrameworkListView as AuditListView } from 'app/customer-request/entities/auditor-framework-list-view.entity';
import { CustomerRequest } from 'app/customer-request/entities/customer-request.entity';
import { CustomerRequestStatus } from 'app/customer-request/enums/customer-request-status.enum';
import { AuditSampleRepository } from 'app/customer-request/repositories/audit-sample.repository';
import { CustomerRequestMessageFileRepository } from 'app/customer-request/repositories/customer-request-message-file.repository';
import { CustomerRequestRepository } from 'app/customer-request/repositories/customer-request.repository';
import { CustomerRequestCoreService } from 'app/customer-request/services/customer-request-core.service';
import { Feature } from 'app/feature-toggling/entities/feature.entity';
import { UserFeature } from 'app/feature-toggling/entities/user-feature.entity';
import { UserFeatureRepository } from 'app/feature-toggling/repositories/user-feature.repository';
import { Framework } from 'app/frameworks/entities/framework.entity';
import { FrameworkRepository } from 'app/frameworks/repositories/framework.repository';
import { FrameworksCoreService } from 'app/frameworks/services/frameworks-core.service';
import { ControlIsReadyView } from 'app/grc/entities/control-is-ready-view.entity';
import { Control } from 'app/grc/entities/control.entity';
import { FacetRunner } from 'app/grc/facets/facet-runner.class';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { ControlsOrchestrationService } from 'app/grc/services/controls-orchestration.service';
import { User } from 'app/users/entities/user.entity';
import { RequestOwnersUsersEntryIdsFacet } from 'app/users/facets/request-owners-users-entry-ids-facet.class';
import { RequestOwnersUsersOutOfScopeFacet } from 'app/users/facets/request-owners-users-out-of-scope-facet.class';
import { RequestOwnersUsersRolesFacet } from 'app/users/facets/request-owners-users-roles-facet.class';
import { RequestOwnersUsersSearchFacet } from 'app/users/facets/request-owners-users-search-facet.class';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { UserRoleRepository } from 'app/users/repositories/user-role.repository';
import { UserRepository } from 'app/users/repositories/user.repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { AuditorsClientsRequestDto } from 'auditor-api/dtos/auditor-clients-request.dto';
import { AuditFirm } from 'auditors/entities/audit-firm.entity';
import { AuditorClient } from 'auditors/entities/auditor-client.entity';
import { AuditorFrameworkAuditors as AuditAuditors } from 'auditors/entities/auditor-framework-auditors.entity';
import { Auditor } from 'auditors/entities/auditor.entity';
import { FrameworkTypeTags } from 'auditors/entities/framework-type-tags.map';
import { AuditorAddedToAuditEvent } from 'auditors/observables/events/auditor-added-to-audit.event';
import { AuditorCreatedEvent } from 'auditors/observables/events/auditor-created.event';
import { AuditorFrameworkUpdatedEvent as AuditUpdatedEvent } from 'auditors/observables/events/auditor-framework-updated.event';
import { AuditorRemovedFromAuditEvent } from 'auditors/observables/events/auditor-removed-from-audit.event';
import { AuditorRevokedEvent } from 'auditors/observables/events/auditor-revoked.event';
import { InviteMagicLinkAuditorEvent } from 'auditors/observables/events/invite-magic-link-auditor.event';
import { AuditFirmRepository } from 'auditors/repositories/audit-firm.repository';
import { AuditorClientRepository } from 'auditors/repositories/auditor-client.repository';
import { AuditorFrameworkRepository as AuditorAuditRepository } from 'auditors/repositories/auditor-framework.repository';
import { AuditorRepository } from 'auditors/repositories/auditor.repository';
import { AuditListType } from 'auditors/types/audit-list.type';
import { Audit } from 'audits/entities/audit.entity';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { EntryService } from 'auth/services/entry.service';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { ControlEvidencePackageGenerationType } from 'commons/enums/control-evidence-package-generation-type.enum';
import { SettledStatus } from 'commons/enums/notifications/settled-status.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { ConflictException } from 'commons/exceptions/conflict.exception';
import { ForbiddenException } from 'commons/exceptions/forbidden.exception';
import { NotFoundException as NotFoundExceptionWithErrorCode } from 'commons/exceptions/not-found.exception';
import { createZipBufferWithPassThrough } from 'commons/helpers/buffer.helper';
import { getInvalidEvidenceNumber } from 'commons/helpers/control.helper';
import { getDomainFromEmail } from 'commons/helpers/domain.helper';
import { getSkip } from 'commons/helpers/pagination.helper';
import { frameworkToFrameworkTemplateEntity } from 'commons/helpers/publish-frameworks.helper';
import { hasRole, isSupportUser } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { FileBufferType } from 'commons/types/file-buffer.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { format } from 'date-fns';
import { Downloader } from 'dependencies/downloader/downloader';
import { Uploader } from 'dependencies/uploader/uploader';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { get, isEmpty, isNil, uniqBy } from 'lodash';
import { ServiceUserRepository } from 'service-user/repositories/service-user.repository';
import { AuditorFrameworkTypeTemplate as AuditTypeTemplate } from 'site-admin/entities/auditor-framework-type-template.entity';
import { EntityNotFoundError, FindOptionsRelations, In, Repository } from 'typeorm';

@Injectable()
export class AuditHubService extends AppService {
    constructor(
        private readonly auditHubValidationService: AuditHubValidationService,
        private readonly auditHubValidationOrchestrationService: AuditHubValidationOrchestrationService,
        private readonly frameworksCoreService: FrameworksCoreService,
        private readonly entryService: EntryService,
        private readonly usersService: UsersCoreService,
        @InjectRepository(AuditTypeTemplate)
        private readonly auditTypeTemplateRepository: Repository<AuditTypeTemplate>,
        private readonly auditRepository: AuditorAuditRepository,
        @InjectRepository(AuditAuditors)
        private readonly auditAuditorsRepository: Repository<AuditAuditors>,
        private readonly auditorRepository: AuditorRepository,
        private readonly serviceUserRepository: ServiceUserRepository,
        private readonly auditFirmRepository: AuditFirmRepository,
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly auditorClientRepository: AuditorClientRepository,
        private readonly companiesCoreService: CompaniesCoreService,
        private readonly downloader: Downloader,
        private readonly uploader: Uploader,
        private readonly grcService: ControlsOrchestrationService,
        private readonly companiesOrchestrationService: CompaniesOrchestrationService,
        private readonly controlEvidenceWaasService: ControlEvidenceWaasService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly collectAuditHubControlEvidenceService: CollectAuditHubControlEvidenceService,
        private readonly customerRequestCoreService: CustomerRequestCoreService,
        private readonly controlService: ControlService,
        private readonly httpService: HttpService,
        private readonly auditPackagesOrchestrationService: AuditHubAuditPackagesOrchestrationService,
    ) {
        super();
    }

    /**
     *
     * @param account The account for which we query personnel information.
     * @param auditId The id of the audit for which need the personnel.
     * @param dto The query params sent with the request.
     * @returns Promise<PaginationType<User>>
     */
    async getAvailableRequestOwnersForAudit(
        account: Account,
        auditId: string,
        dto: AuditAvailableRequestOwnersRequestDto,
    ): Promise<PaginationType<User>> {
        try {
            await this.auditRepository.findOneOrFail({
                where: { id: auditId, account: { id: account.id } },
            });

            const auditorsFromAudit = await this.auditorRepository.getAuditorsByAccountAndAudit(
                auditId,
                account.id,
            );

            dto.auditorEntryIds = auditorsFromAudit.map(auditor => auditor.entry.id);
            dto.roles = [Role.ADMIN, Role.SERVICE_USER];

            const facetRunner = new FacetRunner();

            facetRunner.addFacetsByClass(
                [
                    RequestOwnersUsersSearchFacet,
                    RequestOwnersUsersEntryIdsFacet,
                    RequestOwnersUsersRolesFacet,
                    RequestOwnersUsersOutOfScopeFacet,
                ],
                account,
                this.userRepository,
                dto,
            );

            const { include, exclude } = await facetRunner.run();

            if (isEmpty(include)) {
                return {
                    data: [],
                    page: dto.page,
                    limit: dto.limit,
                    total: 0,
                };
            }

            const dtoExcludeIds = dto.excludeUserIds ?? [];

            return await this.userRepository.getPaginatedUsersByIds(dto, include, [
                ...exclude,
                ...dtoExcludeIds,
            ]);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getAvailableRequestOwnersForAudit.name),
            );

            if (error instanceof EntityNotFoundError) {
                throw new NotFoundException();
            }

            return {} as PaginationType<User>;
        }
    }

    /**
     * @deprecated Use AuditHubOrchestrationService.getAvailablePersonnelForAudit
     *
     * @param account The account for which we query personnel information.
     * @param user The user trying to access the information
     * @param auditId The id of the audit for which need the personnel.
     * @param dto The query params sent with the request.
     * @returns Promise<PaginationType<Personnel>>
     *
     * It tries to retrieve the different personnel (hired, fired or current) related to an audit.
     * It can be accessed either by an auditor, for which we check that he actually can access the audit
     * or by admins for which no check is needed.
     */
    async getAvailablePersonnelForAudit(
        accountId: string,
        entryId: string,
        auditId: string,
        dto: AuditPersonnelRequestDto,
    ): Promise<PaginationType<Personnel>> {
        const { auditPersonnelType, limit, page = 1, q: personnelSearchTerms } = dto;

        const audit = await this.auditRepository.findOneOrFail({
            where: { id: auditId, account: { id: accountId } },
        });

        const user = await this.usersService.getUserWithSupportByEntryId(entryId);

        // if the user is an auditor but is currently acting as an Admin then we should treat them as a tenant user and ignore their auditor role
        if (!hasRole(user, [Role.ADMIN])) {
            await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
                entryId,
                audit.id,
                accountId,
            );
        }

        const { startDate, endDate } = getAuditPeriodByFramework(audit);

        return this.personnelRepository.getPaginatedPersonnelByTypeWithinRange(
            auditPersonnelType,
            startDate,
            endDate,
            page,
            limit,
            personnelSearchTerms,
        );
    }

    /**
     * # getAuditWithAuditorsAndHasControlsFlagOrFail
     *
     * Get `Audit` and Auditors assigned to audit using getAuditWithAuditorsOrFail.
     *
     * This also returns a boolean flag that indicates whether the audit related framework has controls
     */
    async getAuditWithAuditorsAndHasControlsFlagOrFail(
        account: Account,
        user: User,
        auditId: string,
    ): Promise<AuditWithAuditors> {
        const auditWithAuditors = await this.getAuditWithAuditorsOrFail(account.id, user, auditId);

        const {
            audit: { productId, customFrameworkId, frameworkType },
        } = auditWithAuditors;

        const frameworkTag = FrameworkTypeTags.get(frameworkType);

        if (isNil(frameworkTag)) {
            throw new NotFoundException('invalid framework tag');
        }

        const frameworkWithControls =
            await this.frameworkRepository.getFrameworkWithControlsByTagAndWorkspace(
                frameworkTag,
                productId,
                customFrameworkId,
            );

        return {
            audit: auditWithAuditors.audit,
            auditors: auditWithAuditors.auditors,
            auditFrameworkHasControls: !isNil(frameworkWithControls),
        };
    }

    /**
     * @deprecated Use AuditHubOrchestrationService.getAuditWithAuditorsOrFail
     *
     * # getAuditWithAuditorsOrFail
     *
     * Get `Audit` by id doing the
     * proper checks to make sure that, if the user is an auditor,
     * such auditor has access to the requested audit.
     *
     * This method also fetches and returns all the auditors
     * assigned to the audit.
     */
    async getAuditWithAuditorsOrFail(
        accountId: string,
        user: User,
        auditId: string,
    ): Promise<AuditWithAuditors> {
        let audit = await this.auditRepository.getAuditByIdOrFail(auditId, accountId);

        // if the user is an auditor but is currently acting as an Admin then we should treat them as a tenant user and ignore their auditor role
        if (!hasRole(user, [Role.ADMIN])) {
            await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
                user.entryId,
                audit.id,
                accountId,
            );
        }

        if (audit.frameworkType === AuditTypeEnum.CUSTOM) {
            audit = await this.correctAuditorAuditTypeForCustomAudit(audit);
        }

        const auditors = await this.auditorRepository.getAuditorsByAccountAndAudit(
            audit.id,
            accountId,
        );

        return { audit, auditors };
    }

    /**
     * # correctAuditorAuditTypeForCustomAudit
     *
     * Set the necessary values within the audit to handle the special case
     * of custom frameworks.
     *
     * This method returns a new instance of the target audit if a custom
     * frameworks is found, or the original instance otherwise.
     */
    private async correctAuditorAuditTypeForCustomAudit(audit: Audit): Promise<Audit> {
        const customFramework = await this.frameworksCoreService.getEnabledFrameworkByTag(
            FrameworkTag.CUSTOM,
            null,
            audit.customFrameworkId,
        );

        if (!isNil(customFramework)) {
            return Object.assign(Object.create(Object.getPrototypeOf(audit)), audit, {
                auditorFrameworkType: {
                    ...audit.auditorFrameworkType,
                    label: customFramework.name,
                    relatedFramework: frameworkToFrameworkTemplateEntity(customFramework),
                },
            });
        }

        return audit;
    }

    /**
     *
     * @param account The account on which we are trying to update the .
     * @param auditId The id of the audit to be updated.
     * @param requestDto The information of the audit that needs to be updated.
     * @returns Promise<PaginationType<Personnel>>
     *
     * It tries to retrieve the different personnel (hired, fired or current) related to an audit.
     * It can be accessed either by an auditor, for which we check that he actually can access the audit
     * or by admins for which no check is needed.
     */
    async updateAudit(
        account: Account,
        user: User,
        auditId: string,
        requestDto: AuditUpdateRequestDto,
    ): Promise<Audit> {
        const { type, customFrameworkId, workspaceId, auditType } = requestDto;
        // Not filtering by account because we want separate errors for when an audit does not exist
        // and for when someone is trying to access the audit from a different audit. Which is why
        // we make use of auditHubValidationService.validateAuditForAccount.
        const audit = await this.getAccountAuditByIdOrFail(account, auditId);

        const customFramework = await this.getRelatedCustomFrameworkOrFail(
            type,
            customFrameworkId,
            workspaceId,
        );

        audit.frameworkType = type;
        audit.auditorFrameworkType = await this.getUpdatedAuditTemplateByTypeOrFail(type);
        audit.auditType = auditType ?? audit.auditType;
        audit.customFrameworkId = customFramework?.customFrameworkId;
        audit.productId = workspaceId ?? audit.productId;

        if (!isNil(customFramework)) {
            audit.auditorFrameworkType.relatedFramework =
                frameworkToFrameworkTemplateEntity(customFramework);
        }

        await this.validateTypeForAuditOrFail(account, audit, customFramework?.id);

        const updatedAudit = await this.auditRepository.save(audit);

        const [product, hasMultipleProducts] = await Promise.all([
            this.workspacesCoreService.getProductById(audit.productId),
            this.workspacesCoreService.hasMultipleProducts(account),
        ]);

        this._eventBus.publish(
            new AuditUpdatedEvent(
                account,
                user,
                [updatedAudit],
                requestDto,
                product,
                hasMultipleProducts,
            ),
        );

        return updatedAudit;
    }

    /**
     * getUpdatedAuditTemplateByTypeOrFail
     * @param auditTemplateType The type of the audit template (AuditTypeTemplate) to be retrieved.
     * @returns The retrieved audit template or throws a 404 HTTP exception.
     */
    private async getUpdatedAuditTemplateByTypeOrFail(
        auditTemplateType: AuditTypeEnum,
    ): Promise<AuditTypeTemplate> {
        const newAuditTemplate = await this.auditTypeTemplateRepository.findOne({
            where: {
                type: auditTemplateType,
            },
        });

        if (isNil(newAuditTemplate)) {
            throw new NotFoundException(`The Audit Template ${auditTemplateType} can't be found.`);
        }

        return newAuditTemplate;
    }

    /**
     * @deprecated Use auditHubValidationOrchestrationService.getRelatedCustomFrameworkOrFail
     *
     * @param type The type of audit template(AuditTypeTemplate)
     * @param customFrameworkId The id of the framework for which the audit will be conducted.
     * @param workspaceId The id of the workspace to which both the audit and framework will belong to.
     * @returns The custom framework which belongs to the given workspace and identified by the specified id
     *
     * It throws NotFoundException when there is a mismatch between parameters.
     */
    async getRelatedCustomFrameworkOrFail(
        type: AuditTypeEnum,
        customFrameworkId: string | null = null,
        workspaceId: number | null = null,
    ): Promise<Framework | null> {
        if (type === AuditTypeEnum.CUSTOM) {
            const customFramework = await this.frameworkRepository.getEnabledFrameworkByTag(
                FrameworkTag.CUSTOM,
                workspaceId,
                customFrameworkId,
            );

            if (isNil(customFramework)) {
                throw new NotFoundException(
                    `Custom Framework with ID ${customFrameworkId} does not exist`,
                );
            }

            return customFramework;
        }

        return null;
    }

    /**
     * @deprecated Use auditHubValidationOrchestrationService.validateFrameworkForAuditTypeOrFail
     */
    async validateTypeForAuditOrFail(
        account: Account,
        audit: Audit,
        customFrameworkId?: number,
    ): Promise<void> {
        this.auditHubValidationOrchestrationService.validateFrameworkForAuditTypeOrFail(
            audit.auditorFrameworkType.relatedFramework.tag,
            audit.auditType,
        );

        await this.workspacesCoreService.validateWorkspaceFramework({
            workspaceId: audit.productId,
            frameworkId: customFrameworkId ?? audit.auditorFrameworkType.relatedFramework.id,
            frameworkTag: audit.auditorFrameworkType.relatedFramework.tag,
        });
    }

    /**
     * @deprecated Use AuditHubOrchestrationService.getAuditorsByAuditOrFail
     *
     * # getAuditorsByAuditOrFail
     *
     * List auditors for a given `Audit` by id doing the
     * proper checks to make sure that, if the user is an `Auditor`,
     * such auditor has access to the requested audit.
     */
    async getAuditorsByAuditOrFail(
        accountId: string,
        entryId: string,
        auditId: string,
    ): Promise<Auditor[]> {
        const user = await this.usersService.getUserWithSupportByEntryId(entryId);

        // if the user is an auditor but is currently acting as an Admin then we should treat them as a tenant user and ignore their auditor role
        if (!hasRole(user, [Role.ADMIN])) {
            await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
                entryId,
                auditId,
                accountId,
            );
        }

        return this.auditorRepository.getAuditorsByAccountAndAudit(auditId, accountId);
    }

    /**
     * @param account Account on which the request is made and to which the audit must belong to.
     * @param auditId The string id of the audit that must be soft deleted.
     *
     * The method will retrieve the audit and validate if it correctly belongs to the account passed
     * as parameter. Then it soft deletes the audit and the audit to auditors mapping entity.
     */
    async deleteAuditAndAuditorMappingsOrFail(account: Account, auditId: string): Promise<void> {
        const audit = await this.getAccountAuditByIdOrFail(account, auditId, {
            auditorFrameworkAuditors: true,
        });

        const auditAuditorIds = audit.auditorFrameworkAuditors?.map(
            auditAuditorMapping => auditAuditorMapping.id,
        );

        if (!isEmpty(auditAuditorIds)) {
            await this.auditAuditorsRepository.softDelete(auditAuditorIds);
        }

        await this.auditRepository.softDelete(audit.id);
    }

    /**
     * @deprecated Use AuditHubOrchestrationService.getAccountAuditByIdOrFail
     *
     * @param account Account for which we want to retrieve the audit
     * @param auditId the string id trying to be retrieved.
     * @param relations Optional parameter that allows to specify the related entities for the
     *                  retrieved audit. By default it includes the account.
     *
     * @returns Retrieved audit or throws an error.
     *
     * The function will try to retrieve an audit based only on its id. It retrieves also
     * the account to which the audit is related to but it does not filter the query by it. This is done
     * so that we can throw different errors in the case the auditId passed as parameter does not exist (404)
     * or in the case that the specified auditId belongs to another account (404 with specific error code).
     */
    async getAccountAuditByIdOrFail(
        account: Account,
        auditId: string,
        relations?: FindOptionsRelations<Audit>,
    ): Promise<Audit> {
        const audit = await this.auditRepository.findOne({
            where: {
                id: auditId,
            },
            relations: {
                ...relations,
                account: true,
            },
        });

        if (isNil(audit)) {
            throw new NotFoundException(`Audit with ID ${auditId} does not exist`);
        }

        this.auditHubValidationService.validateAuditForAccountOrFail(account, audit);

        return audit;
    }

    /**
     *
     * @param account The account on which the operation is being conducted.
     * @param user The user performing the operation
     * @param auditId The id of the Audit whose auditor mappings should be updated
     * @param auditorIdList The final list of the auditors that should be mapped to the audit
     *
     * The method will take care of updating the associated auditors and also create the related events.
     * If an empty auditorIdList is received, all mappings will be removed.
     * If an existing auditor id present inside the auditorIdList then its mapping will be kept.
     * If an existing auditor id is not present inside the auditorIdList then its mapping will be removed.
     */
    async assignAuditorsToAuditOrFail(
        account: Account,
        user: User,
        auditId: string,
        auditorIdList: string[],
    ): Promise<Audit> {
        const audit = await this.getAccountAuditByIdOrFail(account, auditId, {
            auditorFrameworkAuditors: {
                auditorClient: {
                    entry: {
                        auditor: true,
                    },
                },
            },
        });

        const currentAuditToAuditorsMapping = audit.auditorFrameworkAuditors;

        const allAuditorsToBeAssigned = await this.getAuditorsByIdsAndAccountIdOrFail(
            auditorIdList,
            account,
        );

        const auditToAuditorsDifferenceSubsets = this.getSubsetsOfAuditorMappingsOrFail(
            currentAuditToAuditorsMapping,
            allAuditorsToBeAssigned,
            audit,
            account.id,
        );

        const {
            auditToAuditorsMappingsToBeKept,
            auditToAuditorsMappingsToBeRemoved,
            newAuditToAuditorsMappings,
        } = auditToAuditorsDifferenceSubsets;

        auditToAuditorsMappingsToBeRemoved.forEach(
            auditAuditorMappingToBeRemoved =>
                (auditAuditorMappingToBeRemoved.deletedAt = new Date()),
        );

        await this.auditAuditorsRepository.save([
            ...newAuditToAuditorsMappings,
            ...auditToAuditorsMappingsToBeRemoved,
            ...auditToAuditorsMappingsToBeKept,
        ]);

        await this.publishAuditAuditorsMappingsEvents(
            account,
            audit,
            user,
            auditToAuditorsDifferenceSubsets,
        );

        return audit;
    }

    /**
     *
     * @param auditorIds An array of the ids of the auditor to be retrieved.
     * @param account The account ot which the auditors should have access
     * @returns An array of auditors among the specified that have access to the tenant.
     *
     * This method leverages the AuditorRepository that inner joins the Auditor with the necessary
     * entities that allow to filter auditors that do not have access to a given tenant. It will throw
     * a NotFoundException with ErrorCode.AUDIT_ASSIGNED_AUDITOR_FORBIDDEN in the case one or more
     * of the auditor ids specified in the first parameter does not have access to the tenant information.
     */
    private async getAuditorsByIdsAndAccountIdOrFail(
        auditorIds: string[],
        account: Account,
    ): Promise<Auditor[]> {
        const auditors = await this.auditorRepository.getAuditorsByIdsAndByAccountId(
            auditorIds,
            account.id,
        );

        if (auditors.length < auditorIds.length) {
            const forbiddenAuditorIds = auditorIds.filter(
                auditorId => !auditors.find(auditor => auditorId === auditor.id),
            );
            this.logger.error(
                PolloAdapter.acct(
                    `The following auditor ids are not allowed to access the tenant ${
                        account.domain
                    }: ${forbiddenAuditorIds.join(', ')}.`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.getAuditorsByIdsAndAccountIdOrFail.name),
            );
            throw new NotFoundExceptionWithErrorCode(ErrorCode.AUDIT_ASSIGNED_AUDITOR_FORBIDDEN);
        }

        return auditors;
    }

    /**
     *
     * @param currentAuditToAuditorsMapping The AuditorFrameworkAuditors entities that are related to an Audit before the update
     * @param allAuditorsToBeAssigned Auditor entities of all the auditors to be assigned to an Audit.
     * @param audit The Audit entity for which we are updating the assigned auditors
     * @param accountId The id of the account
     * @returns AuditToAuditorsMappingsDifferenceSubsets
     * AuditToAuditorsMappingsDifferenceSubsets.auditToAuditorsMappingsToBeRemoved is the list AuditorFrameworkAuditors that were
     *                                          assigned to the Audit but they were now removed. This function does not update
     *                                          their deletedAt date but that's what it is supposed to happen.
     * AuditToAuditorsMappingsDifferenceSubsets.auditToAuditorsMappingsToBeKept is the the list of AuditorFrameworkAuditors that
     *                                          were already assigned to the audit and they will still be assigned to it.
     * AuditToAuditorsMappingsDifferenceSubsets.newAuditToAuditorsMappings is the list of newly create AuditorFrameworkAuditors
     *                                          entities that will be assigned to the audit.
     *
     * Based on the current mapping between the Audit and the Auditors this function generates the subsets of the auditors that
     * are supposed to be removed from the audit, the ones that should be kept and the ones that should be added.
     */
    private getSubsetsOfAuditorMappingsOrFail(
        currentAuditToAuditorsMapping: AuditAuditors[],
        allAuditorsToBeAssigned: Auditor[],
        audit: Audit,
        accountId: string,
    ): AuditToAuditorsMappingsDifferenceSubsets {
        const auditorMappingsToBeRemoved = currentAuditToAuditorsMapping.filter(
            auditToAuditorMapping =>
                !isAuditorMappingInAuditorList(allAuditorsToBeAssigned, auditToAuditorMapping),
        );

        const auditorsMappingsToBeKept = currentAuditToAuditorsMapping.filter(
            auditToAuditorMapping =>
                isAuditorMappingInAuditorList(allAuditorsToBeAssigned, auditToAuditorMapping),
        );

        const newAuditorsMappings = allAuditorsToBeAssigned
            .filter(auditor => !isAuditorInAuditorMappingList(auditorsMappingsToBeKept, auditor.id))
            .map(auditor => transformAuditorToAuditAuditor(auditor, audit, accountId));

        return {
            auditToAuditorsMappingsToBeRemoved: auditorMappingsToBeRemoved,
            auditToAuditorsMappingsToBeKept: auditorsMappingsToBeKept,
            newAuditToAuditorsMappings: newAuditorsMappings,
        };
    }

    /**
     *
     * @param account The tenant on which we are operating.
     * @param audit The Audit whose auditor set has been modified.
     * @param user The user performing the operation
     * @param auditToAuditorsMappingSubsets The subsets of the AuditorFrameworkAuditors entities that are obtained from
     *                                      AuditHubService.getAuditAuditorMappingsDifferenceOrFail.
     *
     * It retrieves product information and user entities related to the auditors so that it can publish
     * internal events for when auditors get added or removed from a certain audit.
     */
    private async publishAuditAuditorsMappingsEvents(
        account: Account,
        audit: Audit,
        user: User,
        auditToAuditorsMappingSubsets: AuditToAuditorsMappingsDifferenceSubsets,
    ): Promise<void> {
        const { auditToAuditorsMappingsToBeRemoved, newAuditToAuditorsMappings } =
            auditToAuditorsMappingSubsets;

        const currentProduct = await this.workspacesCoreService.getProductById(audit.productId);

        const auditorUsersToBeRemovedMapByEntryId = await this.getAuditorUsersMapByEntryId(
            auditToAuditorsMappingsToBeRemoved,
        );

        const auditorUsersToBeAddedMapByEntryId = await this.getAuditorUsersMapByEntryId(
            newAuditToAuditorsMappings,
        );

        this.publishAuditorsRemovedEvents({
            account,
            audit,
            auditToAuditorsMappings: auditToAuditorsMappingsToBeRemoved,
            auditorUsersMapByEntryId: auditorUsersToBeRemovedMapByEntryId,
            currentProduct,
            user,
        });

        this.publishNewAuditorsAddedEvents({
            account,
            audit,
            auditToAuditorsMappings: newAuditToAuditorsMappings,
            auditorUsersMapByEntryId: auditorUsersToBeAddedMapByEntryId,
            currentProduct,
            user,
        });
    }

    /**
     *
     * @param auditToAuditorMapping AuditorFrameworkAuditors entities for which to retrieve the related account users
     * @returns A map of the auditor users on the tenant mapped by their entry id.
     *
     * Uses the UsersService to retrieve User entities and then maps them by their entry id so that they can be easily
     * retrieved.
     */
    private async getAuditorUsersMapByEntryId(
        auditToAuditorMapping: AuditAuditors[],
    ): Promise<Record<string, User>> {
        const auditorUsers = await this.usersService.getUsersByEntryIds(
            auditToAuditorMapping.map(
                auditorMappingToBeRemoved => auditorMappingToBeRemoved.auditorClient.entry.id,
            ),
        );

        return auditorUsers.reduce(
            (acc, auditorUser) => ({
                ...acc,
                [auditorUser.entryId]: auditorUser,
            }),
            {} as Record<string, User>,
        );
    }

    /**
     *
     * @param param0 An object containing all the information needed for the event that the auditors have been
     *               added to an audit.
     * @property     param0.account the account on which the operation has been made
     * @property     param0.audit the Audit being updated
     * @property     param0.auditToAuditorsMappings the AuditorFrameworkAuditors entities that are added to the audit.
     * @property     param0.auditorUsersMapByEntryId the map of the auditors being added mapped by their entry id.
     * @property     param0.currentProduct The workspace to which the audit belongs to.
     * @property     param0.user The user making the changes.
     */
    private publishNewAuditorsAddedEvents({
        account,
        audit,
        auditToAuditorsMappings: newAuditToAuditorsMappings,
        auditorUsersMapByEntryId: auditorUsersToBeAddedMapByEntryId,
        currentProduct,
        user,
    }: PublishAuditorMappingsChangedEventParams) {
        for (const auditorToBeAdded of newAuditToAuditorsMappings) {
            const newAuditorUser =
                auditorUsersToBeAddedMapByEntryId[auditorToBeAdded.auditorClient.entry.id];

            this._eventBus.publish(new InviteMagicLinkAuditorEvent(newAuditorUser, account));

            if (!isNil(currentProduct)) {
                this._eventBus.publish(
                    new AuditorAddedToAuditEvent(
                        audit,
                        user,
                        newAuditorUser,
                        account,
                        currentProduct,
                        auditorToBeAdded.updatedAt,
                    ),
                );
            }
        }
    }

    /**
     *
     * @param param0 An object containing all the information needed for the event that the auditors have been
     *               removed from an audit.
     * @property     param0.account the account on which the operation has been made
     * @property     param0.audit the Audit being updated
     * @property     param0.auditToAuditorsMappings the AuditorFrameworkAuditors entities that are removed from the audit.
     * @property     param0.auditorUsersMapByEntryId the map of the auditors being removed mapped by their entry id.
     * @property     param0.currentProduct The workspace to which the audit belongs to.
     * @property     param0.user The user making the changes.
     */
    private publishAuditorsRemovedEvents({
        account,
        audit,
        auditorUsersMapByEntryId: auditorUsersToBeRemovedMapByEntryId,
        currentProduct,
        auditToAuditorsMappings: auditToAuditorsMappingsToBeRemoved,
        user,
    }: PublishAuditorMappingsChangedEventParams) {
        for (const auditorToBeRemoved of auditToAuditorsMappingsToBeRemoved) {
            const removedAuditorUser =
                auditorUsersToBeRemovedMapByEntryId[auditorToBeRemoved.auditorClient.entry.id];

            if (!isNil(currentProduct)) {
                this.publishAuditorRemovedFromAuditEvent({
                    account,
                    audit,
                    currentProduct,
                    userPerformingOperation: user,
                    removedAuditorUser,
                    auditorMappingUpdatedAt: auditorToBeRemoved.updatedAt,
                });
            }
        }
    }

    /**
     * @deprecated Use AuditHubOrchestrationService.getAuditList
     *
     * @param account
     * @param user
     * @param dto
     * @returns
     */
    async getAuditList(
        account: Account,
        user: User,
        dto: AuditPaginatedRequestDto,
    ): Promise<PaginationType<AuditListType>> {
        const entry = await this.entryService.getEntryById(user.entryId);
        const { id: accountId } = account;
        const isReleaseOptimizedAuditHubEndpoint = await this.featureFlagService.evaluateAsTenant(
            {
                category: FeatureFlagCategory.NONE,
                name: FeatureFlag.RELEASE_OPTIMIZED_AUDIT_HUB_AUDITS_ENDPOINT,
                defaultValue: false,
            },
            account,
        );

        // find an auditor for this entry
        let auditorUser = await (isReleaseOptimizedAuditHubEndpoint
            ? this.auditorRepository
                  .createQueryBuilder('auditor')
                  .innerJoinAndSelect('auditor.entry', 'entry')
                  .where('entry.id = :id', { id: entry.id })
                  .getOne()
            : this.auditorRepository.findOne({
                  where: { entry: { id: entry.id } },
              }));

        // find a service user for this entry and account
        const serviceUser = await this.serviceUserRepository.findOne({
            where: { entry: { id: entry.id }, accounts: { id: accountId } },
        });

        // if the user performing this action is both a service user AND an auditor
        // then rely on the dto to determine if we should display all audits or only audits assigned to you
        // if you should only show audits assigned to you then retain the auditor filter
        if (!isNil(serviceUser) && !isNil(auditorUser) && !dto.onlyShowAssignedAudits) {
            auditorUser = null;
        }

        // if the user is an auditor but is currently acting as an Admin then we should treat them as a tenant user and ignore their auditor role
        if (!isNil(auditorUser) && hasRole(user, [Role.ADMIN])) {
            auditorUser = null;
        }

        let paginatedAuditList: PaginationType<Audit>;

        // if user is auditor, get assigned audits
        if (!isNil(auditorUser)) {
            const client = await this.auditHubValidationService.getAuditorClientForTenantOrFail(
                auditorUser,
                accountId,
            );

            paginatedAuditList = await this.auditRepository.getFilteredAuditsByAccountIdAndClientId(
                accountId,
                client.id,
                dto.limit,
                dto.page,
                !!dto.ignoreLimit,
                {
                    auditStatuses: dto.auditStatuses,
                    workspaceId: dto.workspaceId,
                },
                { isReleaseOptimizedAuditHubEndpoint },
            );
        } else {
            // if tenant user, get all audits for account
            paginatedAuditList = await this.auditRepository.getAuditsByAccountId(
                accountId,
                dto.limit,
                dto.page,
                !!dto.ignoreLimit,
                {
                    auditStatuses: dto.auditStatuses,
                    workspaceId: dto.workspaceId,
                },
                { isReleaseOptimizedAuditHubEndpoint },
            );
        }

        // add related framework info to audits with framework type of CUSTOM
        const customFrameworkPromises = paginatedAuditList.data.map(audit =>
            this.correctAuditorAuditTypeForCustomAudit(audit),
        );
        const processedAudits = await Promise.all(customFrameworkPromises);

        // add message counts to audits
        const auditListWitNotifications = await this.populateAuditMessageCountsByAccount(
            processedAudits,
            auditorUser,
        );

        const auditListWithNotificationsAndControlCount = await Promise.all(
            auditListWitNotifications.map(async audit =>
                this.populateAuditFrameworkControlCount(audit),
            ),
        );

        return {
            limit: paginatedAuditList.limit,
            page: paginatedAuditList.page,
            nextPage: paginatedAuditList.nextPage,
            total: paginatedAuditList.total,
            data: auditListWithNotificationsAndControlCount,
        };
    }

    private publishAuditorRemovedFromAuditEvent({
        account,
        currentProduct,
        userPerformingOperation,
        audit,
        removedAuditorUser,
        auditorMappingUpdatedAt,
    }: PublishAuditorRemovedFromAuditEventParams) {
        this._eventBus.publish(
            new AuditorRemovedFromAuditEvent(
                audit,
                userPerformingOperation,
                removedAuditorUser,
                account,
                currentProduct,
                auditorMappingUpdatedAt,
            ),
        );
    }

    async getAvailableAccounts(entryId: string, requestDto: AuditorsClientsRequestDto) {
        return this.auditorClientRepository.getAuditorClients(requestDto, entryId);
    }

    /**
     *
     * @param account the account on which the operation will be performed
     * @param user The use performing the operation, it should only allow admins.
     * @param auditId The id of the audit from which we want to remove the auditor mapping, UUID
     * @param auditorId The id of the auditor which we want to remove from the audit, UUID
     *
     * The method marks the AuditorFrameworkAuditors entry that maps the audit and auditors passed
     * as parameters as soft deleted and then publishes an event to keep track of such change.
     * Throws not found exceptions in case someone that is not allowed to see that audit is trying
     * to perform the operation or if it does not find the workspace we operating on.
     */
    async deleteAuditToAuditorMappingOFail(
        account: Account,
        user: User,
        auditId: string,
        auditorId: string,
    ): Promise<void> {
        const audit = await this.getAccountAuditByIdOrFail(account, auditId, {
            auditorFrameworkAuditors: {
                auditorClient: {
                    entry: {
                        auditor: true,
                    },
                },
            },
        });

        const auditorMapping = audit.auditorFrameworkAuditors.find(auditAuditorMapping =>
            isAuditorInAuditorMappingList([auditAuditorMapping], auditorId),
        );

        if (isNil(auditorMapping)) {
            throw new NotFoundException('Audit to Auditor mapping not found.');
        }

        auditorMapping.deletedAt = new Date();

        await this.auditAuditorsRepository.save(auditorMapping);

        const currentProduct = await this.workspacesCoreService.getProductById(audit.productId);

        if (isNil(currentProduct)) {
            throw new NotFoundException('Could not find the current workspace.');
        }

        const removedAuditorUser = await this.usersService.getUserByEntryId(
            auditorMapping.auditorClient.entry.id,
        );

        this.publishAuditorRemovedFromAuditEvent({
            account,
            currentProduct,
            userPerformingOperation: user,
            audit,
            removedAuditorUser,
            auditorMappingUpdatedAt: auditorMapping.updatedAt,
        });
    }

    /**
     * @deprecated Use AuditHubOrchestrationService.populateAuditFrameworkControlCount
     *
     * @param auditList
     * @returns
     */
    private async populateAuditFrameworkControlCount(
        auditList: AuditListType,
    ): Promise<AuditListType> {
        const { audit } = auditList;
        const { frameworkType, customFrameworkId, productId } = audit;
        const frameworkTag = FrameworkTypeTags.get(frameworkType);

        const framework = await this.frameworkRepository.getFrameworkWithControlsByTagAndWorkspace(
            frameworkTag,
            productId,
            customFrameworkId,
        );

        return {
            ...auditList,
            hasControls: !isNil(framework),
        };
    }

    /**
     * @deprecated Use AuditHubOrchestrationService.populateAuditMessageCountsByAccount
     *
     * @param audits
     * @param auditor
     * @returns
     */
    private async populateAuditMessageCountsByAccount(
        audits: Audit[],
        auditor?: Auditor | null,
    ): Promise<AuditListType[]> {
        const frameworkIds = audits.map(a => a.id);

        const auditListData = await this.auditListViewRepository.find({
            where: { auditorFrameworkId: In(frameworkIds) },
        });

        const auditListMap = new Map(auditListData.map(view => [view.auditorFrameworkId, view]));

        return audits.map(audit => {
            const listView = auditListMap.get(audit.id);
            if (!listView) {
                return {
                    audit,
                    unreadMessages: 0,
                    acceptedRequests: 0,
                    totalRequests: 0,
                    hasControls: false,
                };
            }

            return {
                audit,
                unreadMessages: auditor
                    ? (listView.auditorUnreadMessages ?? 0)
                    : (listView.customerUnreadMessages ?? 0),
                acceptedRequests: listView.acceptedRequests ?? 0,
                totalRequests: listView.totalRequests ?? 0,
                hasControls: false,
            };
        });
    }

    /**
     * A method that adds the user feature that are needed for the auditors.
     * This was originally implemented to allow auditors to access the new control details page when they had
     * access to the tenant as a read only user.
     *
     * @param tenantUser Auditor user entry
     */
    private async addAuditorUserFeatures(tenantUser: User): Promise<UserFeature[]> {
        let auditorFeatures: Feature[] = [];

        if (!isEmpty(AUDITOR_ENABLED_USER_FEATURE_TYPES)) {
            auditorFeatures = await this.featureRepository.find({
                where: {
                    featureType: In(AUDITOR_ENABLED_USER_FEATURE_TYPES),
                },
            });
        }

        let userFeatures: UserFeature[] = [];

        if (!isEmpty(auditorFeatures)) {
            userFeatures = auditorFeatures.map(feature => {
                const userFeature = new UserFeature();
                userFeature.feature = feature;
                userFeature.user = tenantUser;
                userFeature.enabledAt = feature.defaultIsEnabled ? new Date() : null;
                userFeature.value = feature.defaultValue ?? '0';

                return userFeature;
            });

            await this.userFeatureRepository.save(userFeatures);
        }

        return userFeatures;
    }

    /**
     * # createAuditorAndTenantRelationOrFail
     *
     * Create or update the following entities to establish a relation
     * between an auditor and a company:
     *
     * - `Auditor`
     * - `User` (the user an auditor acts as when accessing the tenant's data)
     * - `Entry` (binds `Auditor` and `User`)
     * - `AuditorClient` (binds `Auditor` and `Company`)
     *
     * This method need to allow using a duplicated email since multiple tenants
     * may want to work with the same auditor. If this happens the `Auditor` will
     * be reused as it is.
     *
     * In the case that the `Auditor` already exists for the same tenant the action
     * will be rejected with a conflict exception.
     */
    async createAuditorAndTenantRelationOrFail(
        account: Account,
        user: User,
        dto: AuditorRequestDto,
    ): Promise<Auditor> {
        if (isSupportUser(user.email)) {
            throw new ForbiddenException(
                'Support User is not permitted to create an Auditor.',
                ErrorCode.SUPPORT_USER_NOT_SUPPORTED,
            );
        }

        await this.auditHubValidationService.validateEmailForAuditorUserOrFail(
            dto.email,
            user,
            account,
        );

        const entry = await this.createOrUpdateEntry(dto.email, account);

        const auditorClient = await this.auditorClientRepository.getAuditorClientByAccountAndEntry(
            account,
            entry,
        );

        if (!isNil(auditorClient)) {
            /**
             * If an Auditor exists and is already enabled for a given Account
             * we will prevent it from being updated.
             *
             * The fact that the AuditorClient exists is proof of this.
             */
            throw new ConflictException(
                'The Auditor already exist for this Account',
                ErrorCode.AUDIT_HUB_AUDITOR_EXISTS_FOR_ACCOUNT,
            );
        }

        let auditor = await this.auditorRepository.findOne({
            where: { entry: { id: entry.id } },
        });

        const tenantUser =
            (await this.usersService.findOneByInsensitiveEmail(entry.email)) ??
            (await this.userRepository.createUser(
                dto.email,
                dto.firstName,
                dto.lastName,
                account,
                entry,
                auditor?.drataTermsAgreedAt ?? null,
            ));

        /**
         * When an `Auditor`'s access to a tenant gets revoked, the tenant
         * user is retained to preserve the event log.
         *
         * The `UserRole` is removed from the tenant user.
         */
        const auditorRole = await this.userRoleRepository.findOne({
            where: { user: { id: tenantUser.id }, role: Role.AUDITOR },
        });

        if (isNil(auditorRole)) {
            const userRole = await this.userRoleRepository.createAuditorRole(tenantUser);

            // ensure roles are initialized in case tenantUser is retrieved with createUser
            tenantUser.roles = tenantUser.roles || [];

            tenantUser.roles.push(userRole);

            await this.userRepository.save(tenantUser);
        }

        if (isNil(auditor)) {
            auditor = await this.createAuditor(dto, entry, account, tenantUser);
        }

        const company = await this.companiesCoreService.getCompanyByAccountId(account.id);

        await this.auditorClientRepository.createAuditorClient(
            entry,
            account,
            user,
            dto.allowDownloads,
            dto.readOnly,
            company?.logo,
        );

        const userFeatures = await this.addAuditorUserFeatures(tenantUser);

        tenantUser.features = userFeatures;

        this._eventBus.publish(
            new AuditorCreatedEvent(
                account,
                user,
                { ...dto, frameworks: [] },
                tenantUser,
                auditor,
                [],
            ),
        );

        return auditor;
    }

    /**
     * # deleteAuditorOrFail
     *
     * - 1. Delete `AuditorFrameworkAuditors` (`Auditor` mappings to any `Audit` belonging to the `Account`).
     * - 2. Delete `UserRole` (`Role.AUDITOR` for the tenant `User` mapped to the `Auditor`).
     * - 3. Delete `AuditorClient` (`Auditor` to `Account` relation).
     *
     * Note: this method assumes that the `User` has permission as `Role.ADMIN`
     */
    async deleteAuditorOrFail(account: Account, user: User, auditorId: string): Promise<void> {
        const auditor = await this.auditorRepository.findOneOrFail({
            where: { id: auditorId },
        });

        const auditorClient: AuditorClient | null = await this.auditorClientRepository.findOne({
            where: {
                entry: { id: auditor.entry.id },
                account: { id: account.id },
            },
        });

        if (isNil(auditorClient)) {
            throw new ForbiddenException(
                'Auditor now allowed in Account',
                ErrorCode.AUDIT_HUB_AUDITOR_NOT_AUTHORIZED_IN_ACCOUNT,
            );
        }

        const deletedAuditorToAditsMappings = await this.deleteAuditAuditorMappings(
            auditorClient.id,
        );

        const tenantUser: User = await this.userRepository.findOneOrFail({
            where: { entryId: auditor.entry.id },
        });

        await this.userRoleRepository.softDeleteUserRole(tenantUser.id, Role.AUDITOR);

        await this.auditorClientRepository.softRemove(auditorClient);

        const audits = deletedAuditorToAditsMappings.map(audit => audit.auditorFramework);

        this._eventBus.publish(new AuditorRevokedEvent(account, user, auditor, audits));
    }

    /**
     * # deleteAuditAuditorMappings
     *
     * - Find and soft-delete all `AuditorFrameworkAuditors`
     * for a given `AuditorClient`
     * - For convenience, this method returns the deleted entries.
     */
    private async deleteAuditAuditorMappings(auditorClientId: string): Promise<AuditAuditors[]> {
        const auditorToAuditsMappings = await this.auditAuditorsRepository.find({
            where: {
                auditorClient: { id: auditorClientId },
            },
            relations: {
                auditorFramework: true,
            },
        });

        auditorToAuditsMappings.forEach(auditAuditor => {
            auditAuditor.deletedAt = new Date();
        });

        return this.auditAuditorsRepository.save(auditorToAuditsMappings);
    }

    /**
     * # createAuditor
     *
     * Create an `Auditor` for a tenant `User`.
     */
    private async createAuditor(
        dto: AuditorRequestDto,
        entry: Entry,
        account: Account,
        tenantUser: User,
    ): Promise<Auditor> {
        this.validateAuditorCreationData(dto, entry);

        const { firmName, firstName, lastName, email, language } = dto;
        const drataTermsAgreedAt = get(tenantUser, 'drataTermsAgreedAt', null);
        const accountLanguage = get(account, 'language', null);

        const auditor = new Auditor();

        auditor.firstName = firstName;
        auditor.lastName = lastName;
        auditor.language = language ?? accountLanguage;
        auditor.drataTermsAgreedAt = drataTermsAgreedAt;
        const isAuditorCreatedByEntryId = await this.auditorRepository.findAuditorByEntryId(
            entry.id,
        );

        if (!isNil(isAuditorCreatedByEntryId)) {
            throw new ConflictException(
                'An auditor with this entry is already registered',
                ErrorCode.AUDIT_HUB_AUDITOR_EXISTS_FOR_ACCOUNT,
            );
        }

        auditor.entry = entry;

        let auditFirm: AuditFirm | null = await this.findAuditFirmByDomain(email);
        auditFirm = await this.upsertFirmName(auditFirm, firmName, email);

        if (!isNil(auditFirm)) {
            auditor.auditFirm = auditFirm;
            if (!isNil(auditFirm.name)) {
                auditor.firmName = auditFirm.name;
            }
        }

        return this.auditorRepository.save(auditor);
    }

    private validateAuditorCreationData(createAuditorDto: AuditorRequestDto, entry: Entry): void {
        if (isNil(createAuditorDto)) {
            throw new BadRequestException('Invalid auditor format, values were not provided');
        }

        if (isNil(createAuditorDto.firstName) || isNil(createAuditorDto.lastName)) {
            throw new BadRequestException(
                'Invalid auditor format, firstName and lastName properties are mandatory',
            );
        }

        if (isNil(entry) || isNil(entry.id)) {
            throw new BadRequestException(
                'Invalid auditor format, entry is required for auditor creation',
            );
        }
    }

    private async findAuditFirmByDomain(email: string): Promise<AuditFirm | null> {
        if (isNil(email)) {
            return null;
        }
        return this.auditFirmRepository.findOne({
            where: { domain: getDomainFromEmail(email) },
        });
    }

    async upsertFirmName(
        auditFirm: AuditFirm | null,
        firmName: string,
        email: string,
    ): Promise<AuditFirm | null> {
        if (isNil(firmName)) {
            return null;
        }

        if (!isNil(auditFirm)) {
            return this.auditFirmRepository.updateAuditFirmName(auditFirm, firmName);
        } else if (!isNil(email)) {
            return this.auditFirmRepository.createAuditFirm(getDomainFromEmail(email), firmName);
        }

        return null;
    }

    /**
     * # createOrUpdateEntry
     *
     * Create or update an `Entry` to map an `User` to an `Auditor`.
     *
     * If an `Entry` already exists we add the relation to the `Account` to it.
     */
    private async createOrUpdateEntry(email: string, account: Account): Promise<Entry> {
        const entry = await this.entryService.getEntryByEmailForAuditorNoFail(email);

        if (!isNil(entry)) {
            if (isEmpty(entry.account(account.id))) {
                entry.setEntryAccount(account);
                await this.entryService.saveEntry(entry);
            }

            return entry;
        }

        const newEntry = new Entry();

        newEntry.email = email;
        newEntry.accounts = [account];

        return this.entryService.saveEntry(newEntry);
    }

    async generateRequestControlEvidencePackageEvent(
        account: Account,
        user: User,
        auditId: string,
        customerRequestId: number,
        requestDto: AuditCustomerRequestControlEvidencePackageRequestDto,
    ): Promise<void> {
        const { selectedControlIds } = requestDto;

        /**
         * @deprecated  GenerateAuditCustomerRequestControlEvidencePackageEvent.
         *
         * This event is being deprecated as part of the migration of the Control Evidence Package (CEP)
         * generation process to WaaS (Workflow as a Service).
         * Note: This event will be removed once the WaaS-based process is fully stabilized.
         * */
        this._eventBus.publish(
            new GenerateAuditCustomerRequestControlEvidencePackageEvent(
                account,
                user,
                auditId,
                customerRequestId,
                selectedControlIds ?? [],
            ),
        );
    }

    async getSelectedControlIdsForEvidencePackage(
        account: Account,
        user: User,
        auditId: string,
        customerRequestId: number,
        requestDto: AuditCustomerRequestControlEvidencePackageRequestDto,
    ): Promise<number[]> {
        const { selectAll } = requestDto;
        if (selectAll) {
            return [];
        }
        return requestDto.selectedControlIds ?? [];
    }

    async generateRequestControlEvidencePackageEventViaWaaS(
        account: Account,
        user: User,
        auditId: string,
        customerRequestId: number,
        requestDto: AuditCustomerRequestControlEvidencePackageRequestDto,
    ): Promise<void> {
        const selectedControlIds = await this.getSelectedControlIdsForEvidencePackage(
            account,
            user,
            auditId,
            customerRequestId,
            requestDto,
        );
        const company = await this.companiesCoreService.getCompanyByAccountId(account.id);
        let params: ControlEvidencePackageParams;
        if (!requestDto.selectAll) {
            params = {
                controlIds: selectedControlIds,
                customerRequestId: customerRequestId,
                type: ControlEvidencePackageGenerationType.SELECTED_CONTROLS_IN_REQUEST,
            };
        } else {
            params = {
                customerRequestId: customerRequestId,
                type: ControlEvidencePackageGenerationType.ALL_CONTROLS_IN_REQUEST,
            };
        }

        await this.controlEvidenceWaasService.triggerControlEvidencePackageGeneration(
            account,
            user,
            company,
            auditId,
            params,
        );
    }

    /**
     * @deprecated Use AuditHubOrchestrationService.updateAuditStatusOrFail
     *
     * @param account
     * @param user
     * @param auditId
     * @param status
     */
    async updateAuditStatusOrFail(
        account: Account,
        user: User,
        auditId: string,
        status: AuditOverhaulStatus,
    ): Promise<void> {
        // first I need to validate that the given user has access to the given audit
        await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
            user.entryId,
            auditId,
            account.id,
        );

        try {
            const audit = await this.auditRepository.findOneByOrFail({
                id: auditId,
            });

            audit.status = status;

            if (status === AuditOverhaulStatus.COMPLETED) {
                audit.completedAt = new Date();
                const hasAuditorRole = hasRole(user, [Role.AUDITOR]);
                const hasAdminRole = hasRole(user, [Role.ADMIN]);

                if (hasAuditorRole) {
                    audit.completedRole = Role.AUDITOR;
                } else if (hasAdminRole) {
                    audit.completedRole = Role.ADMIN;
                } else {
                    audit.completedRole = user.roles[0]?.role; // get first role from user
                }
            } else {
                audit.completedAt = null;
                audit.completedRole = null;
            }

            await this.auditRepository.save(audit, {
                reload: false,
            });
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Auditor: ${error.message}`, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateAuditStatusOrFail.name),
            );
        }
    }

    /**
     * @deprecated Use AuditHubOrchestrationService.getPaginatedCustomerRequestMessageFiles
     *
     * Gets a paginated list of the files that are attached to an audit's request message.
     * It validates whether the user's entry id has access to the audit by using the audit's id and
     * the accountId.
     * It then retrieves the paginated list of files. For each files it tries to generated a signed
     * s3 url. In the case the signed url generation fails it returns null for such value (this might
     * occur when the db entry has an empty string in the file column).
     *
     */
    public async getPaginatedCustomerRequestMessageFiles({
        accountId,
        userEntryId,
        auditId,
        requestId,
        messageId,
        page,
        limit,
    }: PaginatedCustomerRequestMessageFilesParams): Promise<
        PaginationType<CustomerRequestMessageFileData>
    > {
        await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
            userEntryId,
            auditId,
            accountId,
        );

        const paginatedData =
            await this.customerRequestMessageFileRepository.getRequestMessageFilesByRequestAndMessageId(
                auditId,
                requestId,
                messageId,
                page,
                limit,
            );

        const signedUrls = await Promise.allSettled(
            paginatedData.data.map(async ({ file }) => {
                const fileData = await this.downloader.getDownloadUrl(file);

                return fileData.signedUrl;
            }),
        );

        const data = paginatedData.data.map(({ id, name }, index) => {
            let url: string | null = null;

            const signedUrlPromiseResult = signedUrls[index];

            if (signedUrlPromiseResult.status === 'fulfilled') {
                url = signedUrlPromiseResult.value || null;
            }

            return {
                id,
                name,
                url,
            } as CustomerRequestMessageFileData;
        });

        return {
            ...paginatedData,
            data,
        };
    }

    /**
     * @deprecated Use AuditHubOrchestrationService.getFirstAuditorUserFromAuditOrFail
     */
    async getFirstAuditorUserFromAuditOrFail(account: Account, audit: Audit): Promise<User> {
        const auditor = await this.auditorRepository.getFirstAuditorByAccountAndAudit(
            audit.id,
            account.id,
        );

        if (isNil(auditor)) {
            throw new NotFoundExceptionWithErrorCode(ErrorCode.AUDITOR_NOT_FOUND);
        }

        return this.usersService.getUserByEntryId(auditor.entry.id);
    }

    async getFedRAMP20xKSIValidation(
        account: Account,
        user: User,
        auditId: string,
    ): Promise<FedRamp20xKSIValidationData> {
        // Validate the audit exists and user has access
        this.log('Getting FedRAMP 20x KSI validation', account, {
            auditId,
        });

        const customSortOrder = [
            RequirementIndexCategoryLabel[RequirementIndexCategory.FR20X_CLOUD_NATIVE_ARCHITECTURE],
            RequirementIndexCategoryLabel[RequirementIndexCategory.FR20X_SERVICE_CONFIGURATION],
            RequirementIndexCategoryLabel[
                RequirementIndexCategory.FR20X_IDENTITY_AND_ACCESS_MANAGEMENT
            ],
            RequirementIndexCategoryLabel[
                RequirementIndexCategory.FR20X_MONITORING_LOGGING_AND_AUDITING
            ],
            RequirementIndexCategoryLabel[RequirementIndexCategory.FR20X_CHANGE_MANAGEMENT],
            RequirementIndexCategoryLabel[RequirementIndexCategory.FR20X_POLICY_AND_INVENTORY],
            RequirementIndexCategoryLabel[
                RequirementIndexCategory.FR20X_THIRD_PARTY_INFORMATION_RESOURCES
            ],
            RequirementIndexCategoryLabel[RequirementIndexCategory.FR20X_CYBERSECURITY_EDUCATION],
            RequirementIndexCategoryLabel[RequirementIndexCategory.FR20X_RECOVERY_PLANNING],
            RequirementIndexCategoryLabel[RequirementIndexCategory.FR20X_INCIDENT_REPORTING],
        ];
        const audit = await this.auditRepository.getAuditByIdWithAuditorsOrFail(auditId);

        await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
            user.entryId,
            audit.id,
            account.id,
        );

        const customerRequests =
            await this.customerRequestRepository.getRequestsWithFrameworksAndControlsByAuditId(
                auditId,
                audit.frameworkType,
                audit.productId,
                account,
            );

        // Process customer requests to build KSI validation data
        const categoryMap = new Map<string, RequirementCategory>();

        customerRequests.sort((a, b) => {
            const categoryA = a.category || '';
            const categoryB = b.category || '';

            const indexA = customSortOrder.indexOf(categoryA);
            const indexB = customSortOrder.indexOf(categoryB);

            if (indexA !== -1 && indexB !== -1) {
                if (indexA !== indexB) {
                    return indexA - indexB;
                }

                // If categories are the same, sort by request code
                const codeA = a.requests && a.requests.length > 0 ? a.requests[0].code : '';
                const codeB = b.requests && b.requests.length > 0 ? b.requests[0].code : '';

                const collator = new Intl.Collator(undefined, {
                    numeric: true,
                    sensitivity: 'base',
                });
                return collator.compare(codeA, codeB);
            }

            return categoryA.localeCompare(categoryB);
        });

        // Group requests by category
        customerRequests.forEach(request => {
            const category = request.category;

            if (!categoryMap.has(category)) {
                categoryMap.set(category, {
                    categoryName: category,
                    requirements: [],
                });
            }

            const categoryGroup = categoryMap.get(category);

            request.requests.forEach(r => {
                const controls = r.controls || [];

                // Determine validation status based on controls' isReady status
                let validationStatus = FedRamp20xKSIValidationStatus.FALSE; // Default if no controls

                if (controls.length > 0) {
                    const readyControls = controls.filter(control => control.isReady === true);

                    if (readyControls.length === controls.length) {
                        validationStatus = FedRamp20xKSIValidationStatus.TRUE; // All controls are ready
                    } else if (readyControls.length > 0) {
                        validationStatus = FedRamp20xKSIValidationStatus.PARTIAL; // Some controls are ready
                    } else {
                        validationStatus = FedRamp20xKSIValidationStatus.FALSE; // No controls are ready
                    }
                }

                categoryGroup?.requirements.push({
                    reqId: r.code,
                    validationStatus: validationStatus,
                    name: r.title,
                    description: r.description,
                    assessorValidationStatus:
                        r.status === CustomerRequestStatus[CustomerRequestStatus.ACCEPTED]
                            ? 'VALIDATED'
                            : '',
                });
            });
        });

        // Generate report data
        return {
            companyName: audit.auditorFrameworkAuditors[0].auditorClient.name,
            assessor:
                audit.auditorFrameworkAuditors[0].auditorClient.entry.auditor.auditFirm?.name || '',
            framework: audit.auditorFrameworkType?.label,
            [RequirementIndexCategoryName[audit.auditorFrameworkType.relatedFramework.tag]]:
                Array.from(categoryMap.values()),
        };
    }

    async getEvidenceData(
        account: Account,
        user: User,
        audit: Audit,
        controls: Control[],
    ): Promise<AuditHubControlEvidenceType[]> {
        // Create a unique control array by control code and separate out of scope controls
        const outOfScopeControls: Control[] = [];
        const inScopeControls = uniqBy(controls, 'code').filter(c => {
            if (!isNil(c.archivedAt)) {
                outOfScopeControls.push(c);
                return false;
            }
            return true;
        });

        const combinedEvidenceResults = await Promise.allSettled([
            this.collectAuditHubControlEvidenceService.getControlRegularEvidence(
                audit,
                inScopeControls,
                account,
                user,
            ),
            this.collectAuditHubControlEvidenceService.getControlSpecialEvidence(
                account,
                audit,
                inScopeControls,
                user,
                outOfScopeControls,
            ),
        ]);

        // Extract successful results and flatten the evidence arrays
        return combinedEvidenceResults
            .filter(
                (result): result is PromiseFulfilledResult<AuditHubControlEvidenceType[]> =>
                    result.status === SettledStatus.FULFILLED,
            )
            .flatMap(result => result.value);
    }

    async getAuditCustomerRequestEvidences(
        account: Account,
        user: User,
        auditId: string,
        customerRequestId: number,
        auditHubEvidencesRequestDto: AuditHubEvidencesPaginatedRequestDto,
    ): Promise<AuditHubEvidencePaginationType> {
        await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
            user.entryId,
            auditId,
            account.id,
        );

        const {
            page = 1,
            limit = 20,
            q = '',
            sort = SortType.NAME,
            sortDir = SortDir.ASC,
            types,
        } = auditHubEvidencesRequestDto;

        const customerRequest =
            await this.customerRequestRepository.getCustomerRequestByIdWithControlsAndTestInstancesOrFail(
                customerRequestId,
            );

        if (isEmpty(customerRequest.controls)) {
            return { data: [], page, limit, total: 0, availableEvidenceTypes: [] };
        }

        const audit = await this.auditRepository.getAuditByIdAndAccount(auditId, account.id);
        if (isNil(audit)) {
            throw new NotFoundException(ErrorCode.AUDIT_NOT_FOUND);
        }

        // Extract successful results and flatten the evidence arrays
        const combinedEvidence = await this.getEvidenceData(
            account,
            user,
            audit,
            customerRequest.controls,
        );

        const filteredEvidences = this.applyEvidenceFilters(combinedEvidence, q, types);

        this.sortEvidences(filteredEvidences, sort, sortDir);

        const skip = getSkip(page, limit);
        const paginatedData = filteredEvidences.slice(skip, skip + limit);

        // Get all available evidence types from the combined evidence (before filtering)
        const availableEvidenceTypes = Array.from(
            new Set(combinedEvidence.map(evidence => evidence.type)),
        );

        return {
            data: paginatedData,
            page,
            limit,
            total: filteredEvidences.length,
            availableEvidenceTypes,
        };
    }

    async getAuditCustomerRequestWithControls(
        account: Account,
        user: User,
        auditId: string,
        customerRequestId: number,
        requestDto: AuditHubControlsPaginatedRequestDto,
    ): Promise<PaginationType<ControlEvidenceCounter>> {
        const customerRequestControls =
            await this.controlService.getPaginatedControlsByCustomerRequestId(
                customerRequestId,
                requestDto,
            );

        const audit = await this.auditRepository.getAuditByIdAndAccount(auditId, account.id);
        if (isNil(audit)) {
            throw new NotFoundException(ErrorCode.AUDIT_NOT_FOUND);
        }

        await Promise.all(
            customerRequestControls.data.map(async control => {
                try {
                    // This is to get number of invalid evidence, will be exposed in the response dto
                    const controlIsReady = await this.controlService.getControlIsReady(control.id);
                    control.controlIsReady =
                        controlIsReady?.controlIsReady as unknown as ControlIsReadyView;
                } catch (error) {
                    this.logger.warn(
                        PolloAdapter.acct(
                            `Could not retrieve control with evidence for control with ID ${control.id}`,
                            account,
                        ).setError(error),
                    );
                }
            }),
        );

        const evidenceData = await this.getEvidenceData(
            account,
            user,
            audit,
            customerRequestControls.data,
        );

        const controlsWithEvidenceCounters = customerRequestControls.data.map(control => {
            const controlEvidences = evidenceData.filter(evidence =>
                evidence.controls.find(c => c.code === control.code),
            );

            return {
                ...control,
                hasEvidence: controlEvidences.length > 0,
                quantityOfRegularValidEvidence: controlEvidences.filter(e =>
                    isRegularEvidence(e.type),
                ).length,
                quantityOfSpecialValidEvidence: controlEvidences.filter(
                    e => !isRegularEvidence(e.type),
                ).length,
                quantityOfInvalidEvidence: getInvalidEvidenceNumber(control),
            } as ControlEvidenceCounter;
        });

        await this.customerRequestCoreService.validateUserAccessForAuditorFramework(user, auditId);

        return { ...customerRequestControls, data: controlsWithEvidenceCounters };
    }

    private applyEvidenceFilters(
        evidences: AuditHubControlEvidenceType[],
        search?: string,
        types?: AuditHubEvidenceType[],
    ): AuditHubControlEvidenceType[] {
        let filtered = evidences;
        if (types && types.length > 0) {
            filtered = filtered.filter(e => types.includes(e.type));
        }
        if (search) {
            const term = search.toLowerCase();
            filtered = filtered.filter(
                e =>
                    e.name.toLowerCase().includes(term) ||
                    e.artifact.toLowerCase().includes(term) ||
                    e.controls.some(c => c.code?.toLowerCase().includes(term)),
            );
        }
        return filtered;
    }

    private sortEvidences(
        evidences: AuditHubControlEvidenceType[],
        sort: SortType,
        sortDir: SortDir,
    ): void {
        evidences.sort((a, b) => {
            let comparison = 0;

            switch (sort) {
                case SortType.NAME:
                    const aName = a.name || '';
                    const bName = b.name || '';
                    comparison = aName.localeCompare(bName);
                    break;
                case SortType.TYPE:
                    const aType = a.type || '';
                    const bType = b.type || '';
                    comparison = aType.localeCompare(bType);
                    break;
                case SortType.DATE:
                    const aDate = new Date(a.date);
                    const bDate = new Date(b.date);
                    comparison = aDate.getTime() - bDate.getTime();
                    break;
                default:
                    const defaultAName = a.name || '';
                    const defaultBName = b.name || '';
                    comparison = defaultAName.localeCompare(defaultBName);
            }

            return sortDir === SortDir.ASC ? comparison : -comparison;
        });
    }

    async generateEvidenceWithFiles(
        account: Account,
        user: User,
        auditId: string,
        customerRequestId: number,
        auditHubEvidencesDownloadRequestDto: AuditHubEvidencesDownloadRequestDto,
    ): Promise<AuditHubControlEvidenceType> {
        const { type: evidenceType, controlCode } = auditHubEvidencesDownloadRequestDto;

        if (
            evidenceType !== AuditHubEvidenceType.PERSONNEL &&
            evidenceType !== AuditHubEvidenceType.TEST_EVIDENCE
        ) {
            throw new BadRequestException(
                `Evidence type ${evidenceType} is not supported. Only PERSONNEL and TEST_EVIDENCE are allowed.`,
            );
        }

        await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
            user.entryId,
            auditId,
            account.id,
        );

        const customerRequest =
            await this.customerRequestRepository.getCustomerRequestByIdWithControlsAndTestInstancesOrFail(
                customerRequestId,
            );

        if (isEmpty(customerRequest.controls)) {
            throw new NotFoundException('No controls found for this customer request');
        }

        const audit = await this.auditRepository.getAuditByIdAndAccount(auditId, account.id);
        if (isNil(audit)) {
            throw new NotFoundException(ErrorCode.AUDIT_NOT_FOUND);
        }

        const controls = customerRequest.controls.filter(c => c.code === controlCode);

        if (isEmpty(controls)) {
            throw new NotFoundException(
                `Control with code ${controlCode} not found in customer request`,
            );
        }

        const control = controls[0];

        const evidences = await this.collectAuditHubControlEvidenceService.generateEvidenceFiles(
            account,
            user,
            audit,
            auditHubEvidencesDownloadRequestDto,
            control,
        );

        let filteredEvidences = evidences;
        if (auditHubEvidencesDownloadRequestDto.artifact) {
            filteredEvidences = evidences.filter(
                evidence => evidence.artifact === auditHubEvidencesDownloadRequestDto.artifact,
            );

            if (filteredEvidences.length === 0) {
                throw new Error(
                    `No evidence found with artifact: ${auditHubEvidencesDownloadRequestDto.artifact}`,
                );
            }
        }

        return filteredEvidences[0];
    }

    async generateAllEvidencesZip(
        account: Account,
        user: User,
        auditId: string,
        customerRequestId: number,
        auditHubEvidencesRequestDto: AuditHubEvidencesPaginatedRequestDto,
    ): Promise<{ signedUrl: string; fileName: string }> {
        const {
            controlId,
            q = '',
            sort = SortType.NAME,
            sortDir = SortDir.ASC,
            types,
        } = auditHubEvidencesRequestDto;

        const { audit, customerRequest } = await this.validateAndGetAuditData(
            account,
            user,
            auditId,
            customerRequestId,
        );

        const filteredEvidences = await this.collectAndFilterEvidences(
            account,
            user,
            audit,
            customerRequest,
            q,
            types,
            sort,
            sortDir,
            controlId,
        );

        const fileBuffers = await this.convertEvidencesToFileBuffers(filteredEvidences, account);

        if (isEmpty(fileBuffers)) {
            throw new NotFoundException('No files found in evidences');
        }

        return this.createAndUploadZipFile(fileBuffers, customerRequest, account);
    }

    private async validateAndGetAuditData(
        account: Account,
        user: User,
        auditId: string,
        customerRequestId: number,
    ): Promise<{ audit: Audit; customerRequest: CustomerRequest }> {
        await this.auditHubValidationService.validateAuditorAccessToAuditOrFail(
            user.entryId,
            auditId,
            account.id,
        );

        const audit = await this.auditRepository.getAuditByIdAndAccount(auditId, account.id);
        if (isNil(audit)) {
            throw new NotFoundException(ErrorCode.AUDIT_NOT_FOUND);
        }

        const customerRequest =
            await this.customerRequestRepository.getCustomerRequestByIdWithControlsAndTestInstancesOrFail(
                customerRequestId,
            );

        return { audit, customerRequest };
    }

    private async collectAndFilterEvidences(
        account: Account,
        user: User,
        audit: Audit,
        customerRequest: CustomerRequest,
        searchQuery: string,
        types: AuditHubEvidenceType[] | undefined,
        sort: SortType,
        sortDir: SortDir,
        controlId?: number,
    ): Promise<AuditHubControlEvidenceType[]> {
        let combinedEvidenceResults: PromiseSettledResult<AuditHubControlEvidenceType[]>[];
        if (controlId) {
            const numericControlId = Number(controlId);
            const control = customerRequest.controls.find(c => c.id === numericControlId);

            if (!control) {
                throw new NotFoundException(
                    `Control with id ${controlId} not found in customer request`,
                );
            }

            combinedEvidenceResults = await Promise.allSettled([
                this.collectAuditHubControlEvidenceService.getControlRegularEvidence(
                    audit,
                    [control],
                    account,
                    user,
                    true,
                ),
            ]);
        } else {
            const outOfScopeControls: Control[] = [];
            const inScopeControls = uniqBy(customerRequest.controls, 'code').filter(c => {
                if (!isNil(c.archivedAt)) {
                    outOfScopeControls.push(c);
                    return false;
                }
                return true;
            });
            combinedEvidenceResults = await Promise.allSettled([
                this.collectAuditHubControlEvidenceService.getControlRegularEvidence(
                    audit,
                    inScopeControls,
                    account,
                    user,
                    true,
                ),
                this.collectAuditHubControlEvidenceService.getControlSpecialEvidence(
                    account,
                    audit,
                    inScopeControls,
                    user,
                    outOfScopeControls,
                    true,
                ),
            ]);
        }

        // Extract successful results and flatten the evidence arrays
        return combinedEvidenceResults
            .filter(
                (result): result is PromiseFulfilledResult<AuditHubControlEvidenceType[]> =>
                    result.status === SettledStatus.FULFILLED,
            )
            .flatMap(result => result.value);
    }

    private async convertEvidencesToFileBuffers(
        evidences: AuditHubControlEvidenceType[],
        account: Account,
    ): Promise<FileBufferType[]> {
        const fileBufferPromises = evidences
            .filter(evidence => evidence.fileData || evidence.signedUrl)
            .map(async (evidence): Promise<FileBufferType | null> => {
                if (evidence.fileData && evidence.fileName) {
                    return {
                        stream: Buffer.from(evidence.fileData, 'base64'),
                        filename: `Evidence/${evidence.fileName}`,
                        fileType: evidence.fileType || 'application/octet-stream',
                    };
                } else if (evidence.signedUrl && evidence.artifact) {
                    try {
                        const buffer = await this.httpService.axiosRef
                            .get(evidence.signedUrl, {
                                responseType: 'arraybuffer',
                                timeout: 10000,
                            })
                            .then(response => Buffer.from(response.data));

                        return {
                            stream: buffer,
                            filename: `Evidence/${evidence.artifact}`,
                            fileType: evidence.fileType || 'application/octet-stream',
                        };
                    } catch (error) {
                        this.logger.warn(
                            PolloAdapter.acct(
                                `Failed to download evidence from URL: ${evidence.signedUrl}. Skipping this evidence.`,
                                account,
                            ).setError(error),
                        );
                        return null;
                    }
                }
                return null;
            });

        const fileBuffers: FileBufferType[] = (await Promise.all(fileBufferPromises)).filter(
            (buffer): buffer is FileBufferType => buffer !== null,
        );

        return fileBuffers;
    }

    private async createAndUploadZipFile(
        fileBuffers: FileBufferType[],
        customerRequest: CustomerRequest,
        account: Account,
    ): Promise<{ signedUrl: string; fileName: string }> {
        const zipFileName = `Evidence-CustomerRequest${customerRequest.code}-${format(new Date(), 'MMddyyyy')}.zip`;

        const { passThrough, upload } = this.uploader.getPassThroughUploader({
            bucket: config.get('aws.s3.appBucket'),
            contentType: 'application/zip',
            uploadType: UploadType.EVENT,
            originalName: zipFileName,
            accountId: account.id,
            subFolder: config.get('archive.subFolder'),
        });

        const uploadPromise = upload.done();

        await createZipBufferWithPassThrough(fileBuffers, [], passThrough, null);

        const uploadResult = await uploadPromise;

        const { signedUrl } = await this.downloader.getDownloadUrl(uploadResult.Key, {
            directDownload: true,
        });

        if (!signedUrl) {
            throw new Error('Failed to generate signed URL for ZIP file');
        }

        return {
            signedUrl,
            fileName: zipFileName,
        };
    }

    async getAuditCustomerRequestControlEvidences(
        account: Account,
        user: User,
        auditId: string,
        customerRequestId: number,
        controlId: number,
    ): Promise<AuditHubControlEvidenceType[]> {
        const { audit, customerRequest } = await this.validateAndGetAuditData(
            account,
            user,
            auditId,
            customerRequestId,
        );

        const numericControlId = Number(controlId);
        const control = customerRequest.controls.find(c => c.id === numericControlId);

        if (!control) {
            throw new NotFoundException(
                `Control with id ${controlId} not found in customer request`,
            );
        }
        const controlEvidence =
            await this.collectAuditHubControlEvidenceService.getControlEvidenceDetails(
                control,
                audit,
                account,
                user,
            );

        return controlEvidence || [];
    }

    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }

    private get frameworkRepository(): FrameworkRepository {
        return this.getCustomTenantRepository(FrameworkRepository);
    }

    private get userRoleRepository(): UserRoleRepository {
        return this.getCustomTenantRepository(UserRoleRepository);
    }

    private get personnelRepository(): PersonnelRepository {
        return this.getCustomTenantRepository(PersonnelRepository);
    }

    private get auditListViewRepository(): Repository<AuditListView> {
        return this.getTenantRepository(AuditListView);
    }

    private get customerRequestRepository(): CustomerRequestRepository {
        return this.getCustomTenantRepository(CustomerRequestRepository);
    }

    private get controlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }

    private get auditSampleRepository(): AuditSampleRepository {
        return this.getCustomTenantRepository(AuditSampleRepository);
    }

    private get customerRequestMessageFileRepository(): CustomerRequestMessageFileRepository {
        return this.getCustomTenantRepository(CustomerRequestMessageFileRepository);
    }

    private get featureRepository(): Repository<Feature> {
        return this.getTenantRepository(Feature);
    }

    private get userFeatureRepository(): UserFeatureRepository {
        return this.getCustomTenantRepository(UserFeatureRepository);
    }
}

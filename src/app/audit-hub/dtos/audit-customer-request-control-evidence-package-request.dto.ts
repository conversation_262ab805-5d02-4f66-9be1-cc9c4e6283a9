import { ApiProperty } from '@nestjs/swagger';
import { CustomerRequestControlsBulkActionRequestDto } from 'app/audit-hub/dtos/audit-customer-request-controls-bulk-action-request.dto';
import { Type } from 'class-transformer';
import { ArrayNotEmpty, IsOptional, ValidateIf } from 'class-validator';
import { IsArrayAndUnique } from 'commons/decorators/is-array-and-unique.decorator';

export class AuditCustomerRequestControlEvidencePackageRequestDto extends CustomerRequestControlsBulkActionRequestDto {
    @ApiProperty({
        type: 'number',
        isArray: true,
        description: 'Control IDs',
        example: [1, 2, 3],
        required: false,
    })
    @IsOptional()
    @IsArrayAndUnique({
        message: 'selectedControlIds must be an array of unique numbers or null',
    })
    @Type(() => Number)
    @ArrayNotEmpty()
    @ValidateIf(dto => !dto.selectAll)
    selectedControlIds?: number[];
}

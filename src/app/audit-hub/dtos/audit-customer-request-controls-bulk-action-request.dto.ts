import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { StringToBoolean } from 'commons/decorators/string-to-boolean.decorator';
import { RequestDto } from 'commons/dtos/request.dto';

export class CustomerRequestControlsBulkActionRequestDto extends RequestDto {
    @ApiProperty({
        type: 'boolean',
        example: false,
        description: 'Select all flag',
        required: false,
    })
    @IsOptional()
    @StringToBoolean()
    selectAll?: boolean;

    @ApiProperty({
        type: 'string',
        example: '<PERSON>',
        description: 'Filter data by searching personnel or groups',
        required: false,
    })
    @IsOptional()
    @IsString()
    q?: string;
}
